#%%
%load_ext autoreload
%autoreload 2
#%%
from repositories import risk_free_rate_postgres_repository
from models import benchmark_model_postgres
#%%
from matplotlib import pyplot as plt

#%%
risk_free_repo = risk_free_rate_postgres_repository.RiskFreeRatePostgresRepository()
#%%
avg_risk_free_rate = risk_free_repo.get_avg_risk_free_rate_in_time_horizon(3)
#%%
avg_risk_free_rate
#%%
current_risk_free_rate = risk_free_repo.get_last_risk_free_rate()
#%%
current_risk_free_rate
#%%
from abc import ABC, abstractmethod
from typing import Optional, List, Dict

import pandas as pd
import numpy as np
from datetime import datetime

from sklearn.linear_model import LinearRegression

from utils import util
from configuration import config as config
from repositories import etf_postgres_repository, risk_free_rate_postgres_repository, asset_class_postgres_repository, metadata_postgres_repository


class PricingModel(ABC):

    def __init__(self):
        self.settings = config.get_settings()
        self.etf_repository = etf_postgres_repository.EtfPostgresRepository()
        self.metadata_repository = metadata_postgres_repository.MetadataPostgresRepository()
        self.risk_free_rate_repository = risk_free_rate_postgres_repository.RiskFreeRatePostgresRepository()
        self.asset_class_repository = asset_class_postgres_repository.AssetClassPostgresRepository()

    @abstractmethod
    def calculate_products_returns_and_covariance(self, avg_risk_free_rate: float, current_risk_free_rate: float, last_date: datetime) -> (np.ndarray, np.ndarray):
        pass


class RegressionPricingModel(PricingModel):

    def __init__(self):
        super(RegressionPricingModel, self).__init__()

    # def calculate_products_returns_and_covariance(self, avg_risk_free_rate: float, current_risk_free_rate: float, last_date: datetime = datetime.today(), save_analytics: bool = False) -> (np.ndarray, np.ndarray):
    def calculate_products_returns_and_covariance(
            self,
            avg_risk_free_rate: float,
            current_risk_free_rate: float,
            last_date: Optional[datetime] = None
    ) -> (pd.DataFrame, pd.DataFrame, list[str]):
        if last_date is None:
            last_date = datetime.today()

        asset_class_returns, last_document_date = self.asset_class_repository.get_returns_in_time_horizon(
            self.settings.time_horizon, last_date
        )
        asset_class_market_caps = self.asset_class_repository.get_market_caps(last_date)
        asset_class_weights = self.__calculate_weights(asset_class_market_caps)

        risk_free_rates, last_document_date = self.risk_free_rate_repository.get_risk_free_rates_in_time_horizon(
            self.settings.time_horizon, last_document_date
        )

        etf_returns = self.etf_repository.get_returns_eur_in_time_horizon(
            self.settings.time_horizon, last_document_date
        )

        ter_mapping = self.metadata_repository.get_ter_for_all_listings()

        asset_class_returns, risk_free_rates, etf_returns = util.synchronize_and_fill_dates(
            [asset_class_returns, risk_free_rates, etf_returns]
        )

        market_portfolio_returns = util.calculate_market_portfolio_returns(
            asset_class_returns, asset_class_weights
        )

        risk_free_rates_daily = util.get_daily_return(risk_free_rates)

        betas = self.__calculate_betas(etf_returns, market_portfolio_returns, risk_free_rates_daily)

        expected_market_portfolio_excess_return = util.get_daily_return(0.0398)
        expected_products_excess_returns_daily = betas * expected_market_portfolio_excess_return
        expected_products_excess_returns = util.get_annual_return(
            expected_products_excess_returns_daily.to_numpy()
        ).reshape(-1, 1)

        expected_products_returns = self.__apply_ter_to_returns(
            expected_products_excess_returns,
            list(etf_returns.columns),
            ter_mapping,
            current_risk_free_rate
        )

        etf_cov = util.get_annual_covariance(etf_returns)

        columns = list(etf_returns.columns)

        return (
            pd.DataFrame(expected_products_returns.reshape(1, -1), columns=columns),
            pd.DataFrame(etf_cov, columns=columns, index=columns),
            columns
        )


    @staticmethod
    def __calculate_weights(market_caps: pd.DataFrame) -> np.ndarray:
        total_sum = market_caps.sum(axis=1)
        weights = market_caps.apply(lambda x: x / total_sum)
        return weights.to_numpy()

    @staticmethod
    def __calculate_beta(product_returns: np.ndarray, market_portfolio_returns: np.ndarray, risk_free_rates: np.ndarray) -> float:
        product_returns, market_portfolio_returns, risk_free_rates = product_returns.reshape(-1, 1), market_portfolio_returns.reshape(-1, 1), risk_free_rates.reshape(-1, 1)
        regression = LinearRegression(fit_intercept=True)
        regression.fit(market_portfolio_returns - risk_free_rates, product_returns - risk_free_rates)
        return regression.coef_.item()

    def __calculate_betas(self, etf_returns: pd.DataFrame, market_portfolio_returns: np.ndarray, risk_free_rates: pd.DataFrame) -> pd.DataFrame:
        betas = {}
        for product in etf_returns.columns:
            betas[product] = self.__calculate_beta(etf_returns[product].values, market_portfolio_returns, risk_free_rates.values)
        return pd.DataFrame(betas, index=['beta'])

    def __apply_ter_to_returns(
            self,
            expected_returns: np.ndarray,
            listing_codes: List[str],
            ter_mapping: Dict[str, float],
            current_risk_free_rate: float
    ) -> np.ndarray:
        ter_values = np.array([ter_mapping.get(code, 0) / 100 for code in listing_codes])

        returns_with_ter = expected_returns - ter_values.reshape(-1, 1)

        returns_with_risk_free = returns_with_ter + current_risk_free_rate

        return returns_with_risk_free
#%%
model = RegressionPricingModel()
#%%
expected_returns, cov, columns = model.calculate_products_returns_and_covariance(avg_risk_free_rate, current_risk_free_rate)

#%%
max(expected_returns.values[0])
#%%
expected_returns
#%%
# Assuming df is your DataFrame
row = expected_returns.iloc[0]

# Get columns sorted by value (descending: largest to smallest)
largest = row.sort_values(ascending=False)

# Get top N largest columns
top_n = largest.head(10)

# Get bottom N smallest columns
bottom_n = largest.tail(10)

print("Top columns with largest values:")
print(top_n)

print("\nColumns with smallest values:")
print(bottom_n)
#%%
variances = np.diag(cov)
#%%
std = np.sqrt(variances)
#%%
std = pd.DataFrame(std.reshape(1, -1), columns=columns)
#%%
# Assuming df is your DataFrame
row = std.iloc[0]

# Get columns sorted by value (descending: largest to smallest)
largest = row.sort_values(ascending=False)

# Get top N largest columns
top_n = largest.head(10)

# Get bottom N smallest columns
bottom_n = largest.tail(20)

print("Top columns with largest values:")
print(top_n)

print("\nColumns with smallest values:")
print(bottom_n)
#%%
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# STEP 1: Get expected returns for the first row (listing codes)
returns_series = expected_returns.iloc[0]
listing_to_isin = {code: code.split(':')[0] for code in returns_series.index}

# STEP 2: Get asset class mapping from ISINs
asset_class_to_isins = metadata_postgres_repository.MetadataPostgresRepository().get_asset_class_ISINs()
isin_to_class = {}
for asset_class, isin_list in asset_class_to_isins.items():
    for isin in isin_list:
        isin_to_class[isin] = asset_class

# STEP 3: Build DataFrame for plotting
plot_df = pd.DataFrame({
    'expected_return': returns_series.values,
    'isin': [listing_to_isin.get(code, None) for code in returns_series.index],
})
plot_df['asset_class'] = plot_df['isin'].map(isin_to_class).fillna('Unknown')

# STEP 4: Define custom palette with Bonds blue, Equities red
unique_classes = plot_df['asset_class'].unique()
default_palette = sns.color_palette('tab10', n_colors=len(unique_classes))

custom_palette = {
    'Bonds': '#4a90e2',
    'Equity': '#d72631',
    'Commodities': '#8d65a3',
    'Real Estate': '#c84bd6',
    'Precious Metals': '#d49242'
}

stack_order = ['Equity', 'Bonds', 'Commodities', 'Real Estate', 'Precious Metals']
plot_df['asset_class'] = pd.Categorical(plot_df['asset_class'], categories=stack_order, ordered=True)

# Fill in any unknown classes with fallback colors
unique_classes = plot_df['asset_class'].unique()
default_palette = sns.color_palette('tab10', n_colors=len(unique_classes))
palette = {}
default_idx = 0
for cls in unique_classes:
    if cls in custom_palette:
        palette[cls] = custom_palette[cls]
    else:
        palette[cls] = default_palette[default_idx]
        default_idx += 1

# STEP 6: Plot histogram
plt.figure(figsize=(12, 6))

ax = sns.histplot(
    data=plot_df,
    x='expected_return',
    hue='asset_class',
    bins=100,
    palette=palette,
    multiple='stack',
    edgecolor=None
)

# Adjust patch alpha and outline for visibility
for patch in ax.patches:
    patch.set_alpha(0.85)  # Keep this if you want transparency

plt.title('Histogram of Expected Returns by Asset Class')
plt.xlabel('Expected Return')
plt.ylabel('Count')
plt.tight_layout()
plt.show()
#%%
std_series = pd.Series(std, index=columns)
#%%
len(columns)
#%%
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# STEP 1: Get expected returns for the first row (listing codes)
returns_series = pd.Series(std, index=columns)
listing_to_isin = {code: code.split(':')[0] for code in returns_series.index}

# STEP 2: Get asset class mapping from ISINs
asset_class_to_isins = metadata_postgres_repository.MetadataPostgresRepository().get_asset_class_ISINs()
isin_to_class = {}
for asset_class, isin_list in asset_class_to_isins.items():
    for isin in isin_list:
        isin_to_class[isin] = asset_class

# STEP 3: Build DataFrame for plotting
plot_df = pd.DataFrame({
    'expected_return': returns_series.values,
    'isin': [listing_to_isin.get(code, None) for code in returns_series.index],
})
plot_df['asset_class'] = plot_df['isin'].map(isin_to_class).fillna('Unknown')

# STEP 4: Define custom palette with Bonds blue, Equities red
unique_classes = plot_df['asset_class'].unique()
default_palette = sns.color_palette('tab10', n_colors=len(unique_classes))

custom_palette = {
    'Bonds': '#4a90e2',
    'Equity': '#d72631',
    'Commodities': '#8d65a3',
    'Real Estate': '#c84bd6',
    'Precious Metals': '#d49242'
}

stack_order = ['Equity', 'Bonds', 'Commodities', 'Precious Metals', 'Real Estate']
plot_df['asset_class'] = pd.Categorical(plot_df['asset_class'], categories=stack_order, ordered=True)

# Fill in any unknown classes with fallback colors
unique_classes = plot_df['asset_class'].unique()
default_palette = sns.color_palette('tab10', n_colors=len(unique_classes))
palette = {}
default_idx = 0
for cls in unique_classes:
    if cls in custom_palette:
        palette[cls] = custom_palette[cls]
    else:
        palette[cls] = default_palette[default_idx]
        default_idx += 1

# STEP 6: Plot histogram
plt.figure(figsize=(12, 6))

ax = sns.histplot(
    data=plot_df,
    x='expected_return',
    hue='asset_class',
    bins=100,
    palette=palette,
    multiple='stack',
    edgecolor=None
)

# Adjust patch alpha and outline for visibility
for patch in ax.patches:
    patch.set_alpha(0.85)  # Keep this if you want transparency

plt.title('Histogram of Standard Deviations by Asset Class')
plt.xlabel('Standard Deviation')
plt.ylabel('Count')
plt.tight_layout()
plt.show()
#%%
from repositories.etf_postgres_repository import EtfRepository
from datetime import datetime
#%%
etf_returns = EtfRepository().get_returns_eur_in_time_horizon(
    3, datetime.today()
)
#%%
etf_returns = etf_returns.ffill().bfill()
#%%
lower_bound = etf_returns.mean(axis=0) - 8 * etf_returns.std(axis=0)
upper_bound = etf_returns.mean(axis=0) + 8 * etf_returns.std(axis=0)
#%%
lower_bound[columns[659]]
#%%
upper_bound[columns[659]]
#%%
outliers = etf_returns[columns[659]][(etf_returns[columns[659]] < lower_bound[columns[659]]) | (etf_returns[columns[659]] > upper_bound[columns[659]])]
#%%
outliers
#%%
etf_returns[columns[659]]
#%%
import numpy as np
log_df = np.log1p(etf_returns)
#%%
log_df.mean(axis=0)
#%%
log_df.std(axis=0)
#%%
identifier = 'IE00BFYN8Y92:EMQQ:LSE:USD'

#%%
lower_bound = log_df[identifier].mean(axis=0) - 5 * log_df[identifier].std(axis=0)
upper_bound = log_df[identifier].mean(axis=0) + 5 * log_df[identifier].std(axis=0)
#%%
lower_bound = log_df.mean(axis=0) - 10 * log_df.std(axis=0)
upper_bound = log_df.mean(axis=0) + 10 * log_df.std(axis=0)
#%%
lower_bound
#%%
upper_bound
#%%
outliers = (log_df < lower_bound) | (log_df > upper_bound)
#%%
outliers
#%%
(outliers.sum(axis=0) > 0).sum()
#%%
outliers.sum(axis=0)[outliers.sum(axis=0) > 0].index.tolist()
#%%
outliers['IE00004PGEY9:JRZE:LSE:GBX'][outliers['IE00004PGEY9:JRZE:LSE:GBX'] == True]
#%%

#%%
connection_params = {
        'dbname': 'financial_data_9zmf',
        'user': 'valusense',
        'password': 'lznTSU7Z6b2aUo1CBvTzjulrUziYn1Uv',
        'host': 'dpg-d14q7tq4d50c73ck8bt0-a.frankfurt-postgres.render.com',
        'port': '5432'
    }
#%%
import psycopg2
from typing import List

def get_asset_listing_codes_by_asset_class(asset_class_name: str, connection_params: dict) -> List[str]:
    with psycopg2.connect(**connection_params) as conn:
        with conn.cursor() as cur:
            query = """
                SELECT al.asset_listing_code
                FROM asset_listings al
                JOIN asset_metadata am ON al.asset_isin = am.isin
                JOIN asset_classes ac ON am.asset_class_id = ac.id
                WHERE ac.name = %s
            """
            cur.execute(query, (asset_class_name,))
            rows = cur.fetchall()

    return [row[0] for row in rows]
#%%
crypto = get_asset_listing_codes_by_asset_class('Cryptocurrencies', connection_params)
#%%
crypto
#%%
variances = np.diag(cov)
#%%
non_finite_variances = np.where(~np.isfinite(variances))
#%%
non_finite_variances
#%%
np.where(variances > 1)[0]
#%%
skart = np.array(columns)[np.where(variances > 0.25)[0]]
#%%
skart
#%%
skart_isins = np.char.split(skart, sep=':')
skart_isins = np.array([x[0] for x in skart_isins])
#%%
skart_isins
#%%
skart_isins_metadata = metadata_postgres_repository.MetadataRepository().get_metadata_for_ISINs(skart_isins.tolist())
#%%
columns
#%%
mask = np.char.find(columns, 'IE00BD4TXV59:WRDUSW:SW:USD') >= 0
np.argwhere(mask)
#%%
variances[507]
#%%
variances
#%%
sorted(skart_isins_metadata, key=lambda x: x.get('fund_size_in_m', 0), reverse=True)
#%%
columns[non_finite_variances[0][0]]
#%%
variances = variances[np.isfinite(variances)]
#%%
variances
#%%
plt.figure(figsize=(10, 6))
plt.hist(variances[variances < 1], bins=50)
#%%
print(len(variances))
print(len(variances[variances < 1]))
print(len(variances[variances < 0.5]))
print(len(variances[variances < 0.4]))
print(len(variances[variances < 0.3]))
print(len(variances[variances < 0.25]))
print(len(variances[variances < 0.2]))
print(len(variances[variances < 0.1]))
print(len(variances[variances < 0.05]))
#%%
plt.hist(variances[variances < 0.01], bins=50)
#%%
plt.hist(variances[variances < 0.005], bins=50)
#%%
plt.hist(variances[variances < 0.001], bins=50)
#%%
non_valid_isins = ['IE00004PGEY9:JRZE:LSE:GBX',
 'IE000COQKPO9:NESP:LSE:GBX',
 'IE000I8KRLL9:SEMI:LSE:GBP',
 'IE000RHYOR04:ERNX:XETRA:EUR',
 'IE00B4YBJ215:SPX4:LSE:GBP',
 'IE00B8J37J31:IJPC:SW:CHF',
 'IE00BD34DB16:SP500S:SW:CHF',
 'IE00BD4TXV59:WRDUSW:SW:USD',
 'IE00BDQZN113:AWESGW:SW:USD',
 'IE00BF4G6Z54:JMRE:LSE:GBX',
 'IE00BFNM3P36:SEGM:LSE:GBP',
 'IE00BFXR5W90:LGAG:LSE:GBX',
 'IE00BFY0GT14:SWLD:LSE:GBP',
 'IE00BFZPF322:AT1:LSE:USD',
 'IE00BHZRR147:FLXC:LSE:USD',
 'IE00BHZRR147:FRCH:LSE:GBP',
 'IE00BK5BQW10:VNRG:LSE:GBP',
 'IE00BK5BR626:VHYG:LSE:GBP',
 'IE00BKS7L097:SPEP:LSE:GBX',
 'IE00BKY40J65:HSUS:LSE:GBP',
 'IE00BKY4W127:COMX:LSE:GBX',
 'IE00BKY58G26:HSXJ:LSE:GBP',
 'IE00BM67HM91:XWES:LSE:GBP',
 'IE00BM67HS53:XSMW:LSE:GBP',
 'IE00BM67HT60:XXTW:LSE:GBP',
 'IE00BNGJJT35:SPEX:LSE:GBX',
 'IE00BNRQM384:EQSG:LSE:GBX',
 'IE00BP2NF958:JREJ:LSE:USD',
 'IE00BWBXM948:GXLK:LSE:GBP',
 'LU1681047236:MSED:LSE:GBX',
 'LU1834986900:CH5:LSE:GBX',
 'LU1940199711:ESGL:LSE:GBP',
 'LU2018762653:STPU:PA:EUR',
 'LU2023678282:DTEC:LSE:GBP',
 'LU2023678878:DIGE:LSE:USD',
 'LU2233156749:JPX4:LSE:USD',
 'LU2233156749:JPX4:PA:EUR']
#%%
metadata_postgres_repository.MetadataRepository().get_metadata_for_ISINs(non_valid_isins)
#%%
from repositories import metadata_postgres_repository
#%%
non_valid_isins_metadata = metadata_postgres_repository.MetadataRepository().get_metadata_for_ISINs(non_valid_isins)
#%%
sorted(non_valid_isins_metadata, key=lambda x: x.get('fund_size_in_m', 0), reverse=True)
#%%
out = ['IE00004PGEY9:JRZE:LSE:GBX',
 'IE000COQKPO9:NESP:LSE:GBX',
 'IE000I8KRLL9:SEMI:LSE:GBP',
 'IE000RHYOR04:ERNX:XETRA:EUR',
 'IE00B4YBJ215:SPX4:LSE:GBP',
 'IE00B8J37J31:IJPC:SW:CHF',
 'IE00BD34DB16:SP500S:SW:CHF',
 'IE00BD4TXV59:WRDA:LSE:GBX',
 'IE00BD4TXV59:WRDUSW:SW:USD',
 'IE00BDQZN113:AWESGW:SW:USD',
 'IE00BF4G6Z54:JMRE:LSE:GBX',
 'IE00BFNM3G45:GPSA:LSE:GBP',
 'IE00BFNM3P36:SEGM:LSE:GBP',
 'IE00BFXR5W90:LGAG:LSE:GBX',
 'IE00BFY0GT14:SWLD:LSE:GBP',
 'IE00BFYN8Y92:EMQQ:LSE:USD',
 'IE00BFZPF322:AT1:LSE:USD',
 'IE00BG8BCY43:JPAS:LSE:GBP',
 'IE00BHZRR147:FLXC:LSE:USD',
 'IE00BHZRR147:FRCH:LSE:GBP',
 'IE00BK5BQW10:VNRG:LSE:GBP',
 'IE00BK5BR626:VHYG:LSE:GBP',
 'IE00BKS7L097:SPEP:LSE:GBX',
 'IE00BKY40J65:HSUS:LSE:GBP',
 'IE00BKY4W127:COMX:LSE:GBX',
 'IE00BKY58G26:HSXJ:LSE:GBP',
 'IE00BM67HK77:XWHS:LSE:GBP',
 'IE00BM67HM91:XWES:LSE:GBP',
 'IE00BM67HN09:XWCS:LSE:GBP',
 'IE00BM67HP23:XWDS:LSE:GBP',
 'IE00BM67HR47:XSSW:LSE:GBP',
 'IE00BM67HS53:XSMW:LSE:GBP',
 'IE00BM67HT60:XXTW:LSE:GBP',
 'IE00BM67HV82:XWIS:LSE:GBP',
 'IE00BNGJJT35:SPEX:LSE:GBX',
 'IE00BNRQM384:EQSG:LSE:GBX',
 'IE00BP2NF958:JREJ:LSE:USD',
 'IE00BWBXM948:GXLK:LSE:GBP',
 'LU1681044993:CSWG:LSE:GBX',
 'LU1681047236:MSED:LSE:GBX',
 'LU1834986900:CH5:LSE:GBX',
 'LU1940199711:ESGL:LSE:GBP',
 'LU2018762653:STPU:PA:EUR',
 'LU2023678282:DTEC:LSE:GBP',
 'LU2023678878:DIGE:LSE:USD',
 'LU2130768844:EABE:LSE:GBP',
 'LU2233156749:JPX4:LSE:USD',
 'LU2233156749:JPX4:PA:EUR']
#%%
non_valid = [
    "IE00BG8BCY43:JPAS:LSE:GBP",
    "IE00BM67HK77:XWHS:LSE:GBP",
    "IE00BLS09N40:0LJI:XETRA:EUR",
    "LU1681044993:CSWG:LSE:GBX",
    "LU1900066033:CHIP:PA:EUR",
    "LU1900066033:SEMG:LSE:GBX",
    "LU1900066033:SEMU:LSE:USD",
    "IE0005YK6564:URNM:LSE:USD",
    "IE000NDWFGA5:URNG:LSE:GBP",
    "FR0010342592:LQQ:PA:EUR",
    "FR0010342592:L8I7:XETRA:EUR",
    "IE00B7Y34M31:US9L:XETRA:EUR",
    "IE00B7Y34M31:3LUS:LSE:GBX",
    "IE00B7Y34M31:3USL:LSE:USD",
    "IE00BD4TXV59:WRDA:LSE:GBX",
    "IE00BFYN8Y92:EMQQ:LSE:USD",
    "IE00BFNM3G45:GPSA:LSE:GBP",
    "IE00B7XD2195:3LSI:LSE:GBX",
    "IE00B7XD2195:3SIL:LSE:USD",
    "IE00B8HGT870:3LGO:LSE:GBX",
    "IE00B8HGT870:3GOL:LSE:USD",
    "IE00BGBN6P67:BCHN:LSE:USD",
    "IE00BM67HR47:XSSW:LSE:GBP",
    "IE00BM67HV82:XWIS:LSE:GBP",
    "IE00BLS09N40:3BAL:LSE:GBX",
    "IE00BLRPRL42:3QQQ:XETRA:EUR",
    "IE00BLRPRL42:LQQ3:LSE:GBX",
    "IE00BLRPRL42:QQQ3:LSE:USD",
    "IE00BMDKNW35:DAPP:LSE:USD",
    "IE00BMDKNW35:DAGB:LSE:GBP",
    "IE00BMDKNW35:DAVV:XETRA:EUR",
    "IE00BM67HN09:XWCS:LSE:GBP",
    "IE00BM67HP23:XWDS:LSE:GBP",
    "LU2130768844:EABE:LSE:GBP"
]
#%%
len(out)
#%%
len(non_valid)
#%%
intersection = list(set(out) & set(non_valid))
print(intersection)
print(len(intersection))
#%%
len(set(out) - set(non_valid))
#%%
len(set(non_valid) - set(out))
#%%
thrown_out = [
    'FR0010342592',
    'IE000RHYOR04',
    'IE00B7XD2195',
    'IE00B7Y34M31',
    'IE00B8HGT870',
    'IE00B8J37J31',
    'IE00BD34DB16',
    'IE00BDQZN113',
    'IE00BLRPRL42',
    'IE00BLS09N40',
    'IE00BMDKNW35',
    'LU2130768844'
]
#%%
from repositories import metadata_postgres_repository
#%%
thrown_out_metadata = metadata_postgres_repository.MetadataPostgresRepository().get_metadata_for_ISINs(thrown_out)
#%%
thrown_out_metadata