from datetime import datetime
from typing import List, Tuple

import pandas as pd

from configuration import logging_config
from repositories.interfaces.risk_free_rate_repository_interface import RiskFreeRateRepositoryInterface
from repositories.interfaces.model_repository_interface import ModelRepositoryInterface

logger = logging_config.get_logger(__name__)


class ModelService:
    def __init__(
            self,
            risk_free_rate_repository: RiskFreeRateRepositoryInterface,
            model_repository: ModelRepositoryInterface,
            model,
            settings
    ):
        self.risk_free_rate_repository = risk_free_rate_repository
        self.model_repository = model_repository
        self.model = model
        self.settings = settings

    def calculate_product_returns_and_covariance(self, calculate_on_date: datetime = None):
        calculate_on_date = calculate_on_date or datetime.today()
        avg_risk_free_rate = self.risk_free_rate_repository.get_avg_risk_free_rate_in_time_horizon(self.settings.time_horizon, calculate_on_date)
        current_risk_free_rate = self.risk_free_rate_repository.get_last_risk_free_rate(calculate_on_date)

        return self.model.calculate_products_returns_and_covariance(avg_risk_free_rate, current_risk_free_rate, calculate_on_date)

    def calculate_and_save_product_returns_and_covariance(self, calculate_on_date: datetime = None):
        calculate_on_date = calculate_on_date or datetime.today()
        expected_returns, covariance = self.calculate_product_returns_and_covariance(calculate_on_date)
        self.model_repository.save_expected_returns(expected_returns)
        self.model_repository.save_covariance(covariance)
        return expected_returns, covariance

    def get_expected_returns_and_covariance(self, asset_listing_codes: List[str] = None, calculate_on_date: datetime = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        calculate_on_date = calculate_on_date or datetime.today()
        logger.info('fetching expected returns and covariance')
        expected_returns = self.model_repository.get_expected_returns_for_asset_listings(asset_listing_codes, calculate_on_date)
        covariance = self.model_repository.get_covariance_for_asset_listings(asset_listing_codes, calculate_on_date)

        logger.info(f'fetched data for {expected_returns.size} isins')

        return expected_returns, covariance

    def get_covariance(self, asset_listing_codes: List[str] = None, calculate_on_date: datetime = None) -> pd.DataFrame:
        calculate_on_date = calculate_on_date or datetime.today()
        return self.model_repository.get_covariance_for_asset_listings(asset_listing_codes, calculate_on_date)

    def get_expected_returns(self, asset_listing_codes: List[str] = None, calculate_on_date: datetime = None) -> pd.DataFrame:
        calculate_on_date = calculate_on_date or datetime.today()
        return self.model_repository.get_expected_returns_for_asset_listings(asset_listing_codes, calculate_on_date)
