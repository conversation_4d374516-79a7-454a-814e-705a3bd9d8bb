from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional, Tuple
import pandas as pd


class AssetClassRepositoryInterface(ABC):
    """
    Interface for asset class repositories.
    Implementations should handle asset class data retrieval operations.
    """

    @abstractmethod
    def get_market_caps(
            self,
            last_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Retrieve the latest market capitalizations for each asset class as of the specified date.

        Args:
            last_date (Optional[datetime]): The last date to consider for market caps.

        Returns:
            pd.DataFrame: A DataFrame with dates as index, asset class names as columns, and market caps as values.
        """
        pass

    @abstractmethod
    def get_returns_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: Optional[datetime] = None
    ) -> Tuple[pd.DataFrame, datetime]:
        """
        Retrieve daily returns for representative asset listings within a specified time horizon up to a given date.

        Args:
            time_horizon_in_years (int): Number of years for the time horizon.
            last_date (Optional[datetime]): The last date to consider for returns.

        Returns:
            Tuple[pd.DataFrame, datetime]: DataFrame of returns and the last document date.
        """
        pass