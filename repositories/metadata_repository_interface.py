from abc import ABC, abstractmethod
from typing import List, Dict, Optional


class MetadataRepositoryInterface(ABC):
    """
    Interface for metadata repositories.
    Implementations should handle asset metadata retrieval operations.
    """

    @abstractmethod
    def get_metadata_for_ISINs(
            self,
            ISINs: Optional[List[str]] = None
    ):
        """
        Get metadata for specified ISINs.

        Args:
            ISINs: Optional list of ISINs to get metadata for

        Returns:
            List of metadata objects
        """
        pass

    @abstractmethod
    def get_fields_for_ISINs(self, ISINs: List[str], fields: List[str]) -> Dict[str, List]:
        """
        Get specific fields for specified ISINs.

        Args:
            ISINs: List of ISINs to get fields for
            fields: List of field names to retrieve

        Returns:
            Dictionary mapping field names to lists of values
        """
        pass

    @abstractmethod
    def get_asset_class_ISINs(self) -> Dict[str, List[str]]:
        """
        Get ISINs grouped by asset class.

        Returns:
            Dictionary mapping asset class names to lists of ISINs
        """
        pass

    @abstractmethod
    def get_ter_for_all_listings(self) -> Dict[str, float]:
        """
        Retrieve the Total Expense Ratio (TER) for listings.

        Returns:
            Dict[str, float]: Mapping from asset listing code to TER.
        """
        pass