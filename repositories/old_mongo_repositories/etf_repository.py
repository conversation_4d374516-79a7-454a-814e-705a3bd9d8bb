from datetime import datetime
from typing import List, Optional, Tuple
from pymongo import MongoClient
import pandas as pd
from dateutil.relativedelta import relativedelta

from configuration import config
from repositories import EtfRepositoryInterface


class EtfRepository(EtfRepositoryInterface):
    """
    Repository for interacting with ETF data (prices, returns, and risk-free data) stored in MongoDB.

    Attributes:
        settings: Configuration settings containing database connection details.
    """

    def __init__(self):
        self.settings = config.get_settings()

    def _get_latest_document(self,
                             collection_name: str,
                             date: Optional[datetime] = datetime.now()
                             ) -> dict:
        """Helper method to fetch the latest document up to a specified date."""
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[collection_name]
            try:
                return collection.find(
                    {'date': {'$lte': date}},
                    {'_id': False}
                ).sort('date', -1).limit(1).next()
            except StopIteration:
                raise ValueError(f"No data found for date <= {date}.")

    def _fetch_data_within_date_range(self,
                                      collection_name: str,
                                      start_date: datetime,
                                      end_date: datetime,
                                      projection: Optional[dict] = None
                                      ) -> List:
        """Helper method to fetch data within a specific date range."""

        if not projection:
            projection = {}

        projection['_id'] = False

        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[collection_name]
            data_within_range = (collection.find(
                {'date': {'$gte': start_date, '$lte': end_date}},
                projection
            ).sort('date', 1))

            data = []

            for doc in data_within_range:
                isin_values = doc.get('isin_values', {})
                isin_values['date'] = doc['date']
                data.append(isin_values)

            return data

    @staticmethod
    def _build_aggregation_pipeline(isins: List[str],
                                    start_date: datetime,
                                    ending_date: datetime,
                                    field_name: str
                                    ) -> List[dict]:
        """Helper method to build aggregation pipeline for querying ISIN data."""
        return [
            {"$match": {"date": {"$gte": start_date, "$lte": ending_date}}},
            {"$project": {"_id": False, "date": True, **{isin: {"$ifNull": [f"${field_name}.{isin}", None]} for isin in isins}}}
        ]

    def _aggregate_data(self,
                        collection_name: str,
                        pipeline: List[dict]
                        ) -> List[dict]:
        """Helper method to aggregate data using a MongoDB pipeline."""
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[collection_name]
            return list(collection.aggregate(pipeline))

    def get_latest_prices_eur(self, last_date: Optional[datetime] = None) -> pd.DataFrame:
        """
        Implementation of the interface method.
        Gets the latest prices in EUR for ETFs.

        Args:
            last_date: Optional date to get prices up to

        Returns:
            DataFrame with prices
        """
        prices_df, _ = self.get_prices_eur(last_date)
        return prices_df

    def get_prices_eur(self, last_date: Optional[datetime] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Fetches the latest ETF prices in EUR and risk-free values up to the specified date.

        Args:
            last_date (datetime, optional): The date to filter the prices. Defaults to the current UTC datetime.

        Returns:
            tuple:
                - pd.DataFrame: A DataFrame containing ETF prices in EUR.
                - pd.DataFrame: A DataFrame containing risk-free prices.
        """

        last_date = last_date or datetime.now()

        prices = self._get_latest_document(self.settings.etf_prices_collection_eur, last_date)

        isin_values = prices.get('isin_values', {})
        risk_free_value = prices.get('risk_free', {})

        prices_df = pd.DataFrame(isin_values, index=[prices['date']])
        prices_df.index.name = 'date'

        risk_free_price_df = pd.DataFrame(risk_free_value, index=[prices['date']])
        risk_free_price_df.index.name = 'date'

        return prices_df, risk_free_price_df

    def get_last_date(self) -> datetime:
        """
        Fetches the date of the most recent ETF return document.

        Returns:
            datetime: The date of the most recent document.
        """
        last_document = self._get_latest_document(self.settings.etf_returns_collection)

        return last_document['date']

    def get_returns_in_time_horizon(self,
                                    time_horizon_in_years: int,
                                    last_date: Optional[datetime] = None
                                    ) -> pd.DataFrame:
        """
        Fetches ETF returns within a specified time horizon.

        Args:
            time_horizon_in_years (int): The time horizon in years.
            last_date (datetime, optional): The upper bound for the data. Defaults to the current UTC datetime.

        Returns:
            pd.DataFrame: A DataFrame containing ETF returns data within the time horizon.
        """
        last_date = last_date or datetime.now()
        last_document = self._get_latest_document(self.settings.etf_returns_collection, last_date)

        start_date = last_document['date'] - relativedelta(years=time_horizon_in_years)

        returns_within_time_horizon = self._fetch_data_within_date_range(self.settings.etf_returns_collection, start_date, last_document['date'])

        returns_df = pd.DataFrame(returns_within_time_horizon)
        returns_df.set_index('date', inplace=True)

        return returns_df

    def get_prices_eur_in_time_horizon_for_isins(self,
                                                 isins: List[str],
                                                 time_horizon_in_years: int,
                                                 ending_date: datetime
                                                 ) -> pd.DataFrame:
        """
        Fetches ETF prices in EUR for specified ISINs within a time horizon.

        Args:
            isins (List[str]): The list of ISINs to fetch prices for.
            time_horizon_in_years (int): The time horizon in years.
            ending_date (datetime): The end date of the time horizon.

        Returns:
            pd.DataFrame: A DataFrame containing ETF prices for the specified ISINs within the time horizon.
        """
        start_date = ending_date - relativedelta(years=time_horizon_in_years)

        pipeline = self._build_aggregation_pipeline(isins, start_date, ending_date, "isin_values")

        prices_within_time_horizon = self._aggregate_data(self.settings.etf_prices_collection_eur, pipeline)

        prices_df = pd.DataFrame(prices_within_time_horizon)
        prices_df.set_index('date', inplace=True)

        return prices_df

    def get_returns_in_time_horizon_for_isins(self,
                                              isins: List[str],
                                              time_horizon_in_years: int,
                                              ending_date: datetime
                                              ) -> pd.DataFrame:
        """
        Fetches ETF returns for specified ISINs within a time horizon.

        Args:
            isins (List[str]): The list of ISINs to fetch prices for.
            time_horizon_in_years (int): The time horizon in years.
            ending_date (datetime): The end date of the time horizon.

        Returns:
            pd.DataFrame: A DataFrame containing ETF returns for the specified ISINs within the time horizon.
        """
        start_date = ending_date - relativedelta(years=time_horizon_in_years)

        pipeline = self._build_aggregation_pipeline(isins, start_date, ending_date, "isin_values")

        returns_within_time_horizon = self._aggregate_data(self.settings.etf_returns_collection, pipeline)

        returns_df = pd.DataFrame(returns_within_time_horizon)
        returns_df.set_index('date', inplace=True)

        return returns_df

    def get_returns_with_risk_free_between_dates_for_isins(self,
                                                           isins: List[str],
                                                           starting_date: datetime,
                                                           ending_date: datetime
                                                           ) -> pd.DataFrame:
        pipeline = [
            {
                "$match": {
                    "date": {
                        "$gte": starting_date,
                        "$lte": ending_date
                    }
                }
            },
            {
                "$project": {
                    "_id": False,
                    "date": True,
                    "isin_values": True,
                    "risk_free": True,
                    "isin_array": {
                        "$setUnion": [
                            {"$objectToArray": "$isin_values"},
                            {"$objectToArray": "$risk_free"}
                        ]
                    }
                }
            },
            {
                "$unwind": "$isin_array"  # Flatten the array of ISIN key-value pairs
            },
            {
                "$match": {
                    "isin_array.k": {"$in": isins}  # Filter only the ISINs we're interested in
                }
            },
            {
                "$group": {
                    "_id": "$date",
                    "isin_data": {
                        "$push": {
                            "isin": "$isin_array.k",
                            "value": "$isin_array.v"
                        }
                    }
                }
            },
            {
                "$project": {
                    "date": "$_id",
                    "isin_data": {
                        "$arrayToObject": {
                            "$map": {
                                "input": "$isin_data",
                                "as": "data",
                                "in": {
                                    "k": "$$data.isin",
                                    "v": "$$data.value"
                                }
                            }
                        }
                    }
                }
            },
            {
                "$project": {
                    '_id': False,
                    "date": True,
                    # Use the $ifNull operator to handle potential nulls
                    **{isin: {"$ifNull": [f"$isin_data.{isin}", None]} for isin in isins}
                }
            },
            {
                "$sort": {
                    "date": 1  # Sort in ascending order (1 for ascending, -1 for descending)
                }
            },
        ]

        returns_within_time_horizon = self._aggregate_data(self.settings.etf_returns_collection, pipeline)

        returns_df = pd.DataFrame(returns_within_time_horizon)
        returns_df.set_index('date', inplace=True)

        return returns_df

    def get_dates_in_time_horizon(self,
                                  time_horizon_in_years: int,
                                  last_date: Optional[datetime] = None
                                  ) -> pd.DataFrame:
        """
        Fetches all dates within a specified time horizon up to a specified last date.

        Args:
            time_horizon_in_years (int): The time horizon in years.
            last_date (datetime, optional): The upper bound for the dates. Defaults to the current UTC datetime.

        Returns:
            pd.DataFrame: A DataFrame containing the dates within the time horizon.
        """
        last_date = last_date or datetime.now()

        last_document = self._get_latest_document(self.settings.etf_returns_collection, last_date)
        start_date = last_document['date'] - relativedelta(years=time_horizon_in_years)

        dates_within_time_horizon = self._fetch_data_within_date_range(self.settings.etf_returns_collection, start_date, last_document['date'], {'date': True})

        dates_df = pd.DataFrame(dates_within_time_horizon)

        return dates_df

    def save_simulated_returns(self, simulated_returns: pd.DataFrame):
        simulations = simulated_returns.copy()
        simulations['date'] = datetime.today()

        records = simulations.to_dict(orient='records')

        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[self.settings.etf_simulations_collection]

            collection.drop()

            result = collection.insert_many(records)

        return

    def get_simulations_for_isins(self, isins: List[str], n_simulations: int = None) -> pd.DataFrame:
        projection = {'_id': False}

        if isins is not None:
            for isin in isins:
                projection[isin] = 1
        else:
            projection['date'] = 0

        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[self.settings.etf_simulations_collection]

            if n_simulations is None:
                data = list(collection.find({}, projection=projection))
            else:
                data = list(collection.find({}, projection=projection, limit=n_simulations))

            return pd.DataFrame(data)
