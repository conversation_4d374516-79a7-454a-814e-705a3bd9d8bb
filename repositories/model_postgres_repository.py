import psycopg2
import pandas as pd
from datetime import datetime
from typing import List, Optional
from configuration import config, config_params

class ModelPostgresRepository:
    def __init__(self):
        self.connection_params = config_params.CONNECTION_PARAMS
        self.expected_returns_table = config_params.ASSET_LISTING_EXPECTED_RETURNS_TABLE
        self.covariance_table = config_params.ASSET_LISTING_COVARIANCES_TABLE

    def get_connection(self):
        return psycopg2.connect(**self.connection_params)

    def save_expected_returns(self, expected_returns: pd.DataFrame, date: Optional[datetime] = None):
        """
        Save expected returns for a given date using asset_listing_code.
        Overwrites any existing records for that date.
        """
        date = date or datetime.now()
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                for _, row in expected_returns.iterrows():
                    cur.execute(
                        f"""
                        INSERT INTO {self.expected_returns_table}
                            (asset_listing_id, calculation_date, expected_return)
                        VALUES (
                            (SELECT id FROM asset_listings WHERE asset_listing_code = %s),
                            %s,
                            %s
                        )
                        ON CONFLICT (asset_listing_id, calculation_date) DO UPDATE
                        SET expected_return = EXCLUDED.expected_return
                        """,
                        (row['asset_listing_code'], date, row['expected_return'])
                    )

    def get_latest_expected_returns_for_asset_listings(
            self,
            asset_listing_codes: Optional[List[str]] = None,
            date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Fetch the latest expected return for each asset_listing_code up to a given date.
        Returns a DataFrame with columns ['asset_listing_code', 'expected_return'].
        """
        date = date or datetime.now()
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                sql = f"""
                    SELECT DISTINCT ON (al.asset_listing_code)
                        al.asset_listing_code,
                        er.expected_return
                    FROM {self.expected_returns_table} er
                    JOIN asset_listings al ON er.asset_listing_id = al.id
                    WHERE er.calculation_date <= %s
                """
                params = [date]
                if asset_listing_codes:
                    sql += " AND al.asset_listing_code = ANY(%s)"
                    params.append(asset_listing_codes)
                sql += " ORDER BY al.asset_listing_code, er.calculation_date DESC"
                cur.execute(sql, params)
                rows = cur.fetchall()
                columns = ['asset_listing_code', 'expected_return']
        return pd.DataFrame(rows, columns=columns) if rows else pd.DataFrame(columns=columns)

    def save_covariance(self, covariance_df: pd.DataFrame, date: Optional[datetime] = None):
        """
        Save covariance matrix for a given date using asset_listing_code.
        Only upper triangle (including diagonal) is stored.
        Overwrites any existing records for that date.
        """
        date = date or datetime.now()
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                codes = covariance_df.index.tolist()
                for i, code1 in enumerate(codes):
                    for j, code2 in enumerate(codes):
                        if j >= i:
                            cur.execute(
                                f"""
                                INSERT INTO {self.covariance_table}
                                    (asset_listing_id1, asset_listing_id2, covariance, calculation_date)
                                VALUES (
                                    (SELECT id FROM asset_listings WHERE asset_listing_code = %s),
                                    (SELECT id FROM asset_listings WHERE asset_listing_code = %s),
                                    %s, %s
                                )
                                ON CONFLICT (asset_listing_id1, asset_listing_id2, calculation_date) DO UPDATE
                                SET covariance = EXCLUDED.covariance
                                """,
                                (code1, code2, float(covariance_df.iloc[i, j]), date)
                            )

    def get_latest_covariance_for_asset_listings(
            self,
            asset_listing_codes: Optional[List[str]] = None,
            date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Fetch the latest covariance submatrix for a list of asset_listing_codes up to a given date.
        Returns a symmetric DataFrame with asset_listing_codes as both index and columns.
        """
        date = date or datetime.now()
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                query = f"""
                    SELECT DISTINCT ON (al1.asset_listing_code, al2.asset_listing_code)
                        al1.asset_listing_code AS asset_listing_code1,
                        al2.asset_listing_code AS asset_listing_code2,
                        c.covariance
                    FROM {self.covariance_table} c
                    JOIN asset_listings al1 ON c.asset_listing_id1 = al1.id
                    JOIN asset_listings al2 ON c.asset_listing_id2 = al2.id
                    WHERE c.calculation_date <= %s
                """
                params = [date]
                if asset_listing_codes:
                    query += " AND al1.asset_listing_code = ANY(%s) AND al2.asset_listing_code = ANY(%s)"
                    params.extend([asset_listing_codes, asset_listing_codes])
                query += " ORDER BY al1.asset_listing_code, al2.asset_listing_code, c.calculation_date DESC"
                cur.execute(query, params)
                rows = cur.fetchall()
                columns = ['asset_listing_code1', 'asset_listing_code2', 'covariance']
        if not rows:
            return pd.DataFrame(0.0, index=asset_listing_codes, columns=asset_listing_codes)
        df = pd.DataFrame(rows, columns=columns)
        matrix = df.pivot(index='asset_listing_code1', columns='asset_listing_code2', values='covariance')
        if asset_listing_codes:
            matrix = matrix.reindex(index=asset_listing_codes, columns=asset_listing_codes)
        full_matrix = matrix.combine_first(matrix.T)
        return full_matrix

