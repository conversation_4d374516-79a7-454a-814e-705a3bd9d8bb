from datetime import datetime
from dateutil.relativedelta import relativedelta
from typing import Tuple, Optional
import pandas as pd
import psycopg2
from psycopg2.extras import RealDictCursor
from configuration import config, config_params
from repositories.risk_free_rate_repository_interface import RiskFreeRateRepositoryInterface


class RiskFreeRatePostgresRepository(RiskFreeRateRepositoryInterface):
    def __init__(self):
        self.connection_params = config_params.CONNECTION_PARAMS

    def get_connection(self):
        return psycopg2.connect(**self.connection_params)

    def get_risk_free_rates_in_time_horizon(
        self,
        time_horizon_in_years: int,
        last_date: Optional[datetime] = None
    ) -> Tuple[pd.DataFrame, datetime]:
        if not last_date:
            last_date = datetime.now()

        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT rate_date
                    FROM risk_free_rates
                    WHERE rate_date <= %s
                    ORDER BY rate_date DESC
                    LIMIT 1
                """, (last_date,))
                last_row = cur.fetchone()

                if not last_row:
                    raise ValueError("No risk-free rate data found before the specified date.")

                last_document_date = last_row['rate_date']
                start_date = last_document_date - relativedelta(years=time_horizon_in_years)

                cur.execute("""
                    SELECT 
                        rate_date AS date,  
                        rate::float
                    FROM risk_free_rates
                    WHERE rate_date BETWEEN %s AND %s
                    ORDER BY rate_date ASC
                """, (start_date, last_document_date))

                rows = cur.fetchall()

        risk_free_rates_df = pd.DataFrame(rows)
        risk_free_rates_df.set_index('date', inplace=True)

        return risk_free_rates_df, last_document_date

    def get_avg_risk_free_rate_in_time_horizon(
        self,
        time_horizon_in_years: int,
        last_date: Optional[datetime] = None
    ) -> float:
        if not last_date:
            last_date = datetime.now()

        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT rate_date
                    FROM risk_free_rates
                    WHERE rate_date <= %s
                    ORDER BY rate_date DESC
                    LIMIT 1
                """, (last_date,))
                last_row = cur.fetchone()

                if not last_row:
                    return 0.0

                last_document_date = last_row[0]
                start_date = last_document_date - relativedelta(years=time_horizon_in_years)

                cur.execute("""
                    SELECT AVG(rate)::float AS average_rate
                    FROM risk_free_rates
                    WHERE rate_date BETWEEN %s AND %s
                """, (start_date, last_document_date))
                avg_rate = cur.fetchone()

        return avg_rate[0] if avg_rate and avg_rate[0] is not None else 0.0

    def get_last_risk_free_rate(
        self,
        last_date: Optional[datetime] = None
    ) -> float:
        if last_date is None:
            last_date = datetime.now()

        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT rate::float
                    FROM risk_free_rates
                    WHERE rate_date <= %s
                    ORDER BY rate_date DESC
                    LIMIT 1
                """, (last_date,))
                last_rate = cur.fetchone()

        return last_rate[0] if last_rate else 0.0

    def get_risk_free_rate_for_dates(
        self,
        dates_df: pd.DataFrame
    ) -> float:
        dates_list = dates_df['date'].tolist()

        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT AVG(rate)::float AS average_rate
                    FROM risk_free_rates
                    WHERE rate_date = ANY(%s)
                """, (dates_list,))
                avg_rate = cur.fetchone()

        return avg_rate[0] if avg_rate and avg_rate[0] is not None else 0.0
