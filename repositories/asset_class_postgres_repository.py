import psycopg2
import pandas as pd
from datetime import datetime
from dateutil.relativedelta import relativedelta
from typing import Tuple
from configuration import config_params
from repositories.asset_class_repository_interface import AssetClassRepositoryInterface


class AssetClassPostgresRepository(AssetClassRepositoryInterface):
    def __init__(self):
        self.connection_params = config_params.CONNECTION_PARAMS

    def get_connection(self):
        return psycopg2.connect(**self.connection_params)

    def get_market_caps(self, last_date: datetime = None) -> pd.DataFrame:
        last_date = last_date or datetime.now()

        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                            WITH latest_caps AS (SELECT DISTINCT ON (acmc.asset_class_id) acmc.asset_class_id,
                                                                                          acmc.cap_date,
                                                                                          acmc.market_cap,
                                                                                          acmc.currency,
                                                                                          ac.name AS asset_class_name
                                                 FROM asset_class_market_caps acmc
                                                          JOIN asset_classes ac ON acmc.asset_class_id = ac.id
                                                 WHERE acmc.cap_date <= %s
                                                 ORDER BY acmc.asset_class_id, acmc.cap_date DESC)
                            SELECT cap_date, asset_class_name, market_cap::float
                            FROM latest_caps
                            """, (last_date,))

                rows = cur.fetchall()
                columns = [desc[0] for desc in cur.description]

        if not rows:
            return pd.DataFrame()

        market_caps_df = pd.DataFrame(rows, columns=columns)

        # Pivot: index = date, columns = asset class name, values = market cap
        pivoted_market_caps = market_caps_df.pivot_table(
            index='cap_date',
            columns='asset_class_name',
            values='market_cap'
        )

        pivoted_market_caps.sort_index(inplace=True)
        return pivoted_market_caps

    def get_returns_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: datetime = None
    ) -> Tuple[pd.DataFrame, datetime]:
        last_date = last_date or datetime.now()

        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                            SELECT MAX(data_date)
                            FROM asset_listing_data
                            WHERE data_date <= %s
                            """, (last_date,))
                last_doc_date = cur.fetchone()[0]

                if not last_doc_date:
                    raise ValueError(f"No return data found for date <= {last_date}.")

                start_date = last_doc_date - relativedelta(years=time_horizon_in_years)

                cur.execute("""
                            SELECT ald.data_date,
                                   al.asset_listing_code,
                                   ald.daily_return_eur::float
                            FROM asset_listing_data ald
                                     JOIN asset_listings al ON ald.asset_listing_id = al.id
                            WHERE al.is_representative = TRUE
                              AND ald.data_date BETWEEN %s AND %s
                            ORDER BY ald.data_date ASC
                            """, (start_date, last_doc_date))

                rows = cur.fetchall()
                columns = [desc[0] for desc in cur.description]

        if not rows:
            raise ValueError(f"No return data found between {start_date.date()} and {last_doc_date.date()}.")

        returns_df = pd.DataFrame(rows, columns=columns)

        pivoted_returns = returns_df.pivot_table(
            index='data_date',
            columns='asset_listing_code',
            values='daily_return_eur'
        )

        # Ensure the index is sorted
        pivoted_returns.sort_index(inplace=True)

        return pivoted_returns, last_doc_date
