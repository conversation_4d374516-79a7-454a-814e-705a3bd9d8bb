from typing import List, Dict, Optional, Type
import psycopg2
from configuration import config, config_params
from schemas.pydantic_models import AssetMetadata
from repositories.metadata_repository_interface import MetadataRepositoryInterface
from pydantic import BaseModel, TypeAdapter

class MetadataPostgresRepository(MetadataRepositoryInterface):
    def __init__(self):
        self.connection_params = config_params.CONNECTION_PARAMS

    def _parse_rows_as_model(self, rows: List[dict], model: Type[BaseModel]):
        """Parse database rows as Pydantic models."""
        adapter = TypeAdapter(List[model])
        return adapter.validate_python(rows)

    def get_connection(self):
        return psycopg2.connect(**self.connection_params)

    def get_asset_metadata_columns(self, exclude_cols: list[str] = None) -> list[str]:
        with self.get_connection() as conn:
            if exclude_cols is None:
                exclude_cols = []

            sql = """
                  SELECT column_name
                  FROM information_schema.columns
                  WHERE table_name = %s
                    AND column_name NOT IN %s
                  ORDER BY ordinal_position 
                  """

            with conn.cursor() as cur:
                cur.execute(sql, (config_params.ASSET_METADATA_TABLE, tuple(exclude_cols)))
                return [row[0] for row in cur.fetchall()]

    def get_metadata_for_ISINs(self, ISINs: Optional[List[str]] = None) -> List[AssetMetadata]:
        with self.get_connection() as conn:
            # get all columns except asset_class_id
            cols = self.get_asset_metadata_columns(exclude_cols=['asset_class_id', 'created_at', 'updated_at'])
            select_columns = ", ".join(f"am.{col}" for col in cols)
            select_columns += ", ac.name AS asset_class_name"

            base_query = f"""
                SELECT {select_columns}
                FROM asset_metadata am
                JOIN asset_classes ac ON am.asset_class_id = ac.id
            """

            with conn.cursor() as cur:
                if ISINs:
                    placeholders = ','.join(['%s'] * len(ISINs))
                    query = f"{base_query} WHERE am.isin IN ({placeholders})"
                    cur.execute(query, ISINs)
                else:
                    cur.execute(base_query)

                rows = cur.fetchall()
                columns = [desc[0] for desc in cur.description]
                row_dicts = [dict(zip(columns, row)) for row in rows]

        return self._parse_rows_as_model(row_dicts, AssetMetadata)

    def get_ter_for_all_listings(self) -> Dict[str, float]:
        with self.get_connection() as conn, conn.cursor() as cur:
            sql = """
                  SELECT al.asset_listing_code, am.ter::float
                  FROM asset_metadata am
                  JOIN asset_listings al ON am.isin = al.asset_isin
                  WHERE am.distribution_policy = 'Accumulating'
                  """
            cur.execute(sql)
            return {row[0]: row[1] for row in cur.fetchall()}

    def get_fields_for_ISINs(self, ISINs: List[str], fields: List[str]) -> Dict[str, List]:
        if not fields:
            return {}

        result_dict = {field: [] for field in fields}
        table_name = 'asset_metadata'

        with self.get_connection() as conn, conn.cursor() as cur:
            fields_sql = ", ".join(fields)
            sql = f"SELECT {fields_sql} FROM {table_name} WHERE isin = ANY(%s)"
            cur.execute(sql, (ISINs,))
            for row in cur.fetchall():
                for i, field in enumerate(fields):
                    result_dict[field].append(row[i])

        return result_dict

    def get_asset_class_ISINs(self) -> Dict[str, List[str]]:
        sql = """
            SELECT c.name, array_agg(DISTINCT m.isin)
            FROM asset_metadata m
            JOIN asset_classes c ON m.asset_class_id = c.id
            WHERE m.distribution_policy = 'Accumulating'
            GROUP BY c.name
        """
        with self.get_connection() as conn, conn.cursor() as cur:
            cur.execute(sql)
            return {row[0]: row[1] for row in cur.fetchall()}


    def get_metadata_with_listings(self) -> List[Dict]:
        """
        Returns metadata enriched with listing info (one row per listing).
        """
        sql = """
            SELECT 
                am.*, 
                al.asset_listing_code,
                al.exchange_id,
                al.ticker,
                al.trade_currency,
                al.is_representative,
                al.is_in_default_selection,
                al.is_valid
            FROM asset_metadata am
            JOIN asset_listings al ON am.isin = al.asset_isin
            WHERE am.distribution_policy = 'Accumulating'
        """
        results = []
        with self.get_connection() as conn, conn.cursor() as cur:
            cur.execute(sql)
            rows = cur.fetchall()
            columns = [desc[0] for desc in cur.description]
            for row in rows:
                results.append(dict(zip(columns, row)))
        return results

    def get_listing_codes_for_ISINs(self, ISINs: List[str]) -> Dict[str, List[str]]:
        """
        Returns a mapping from ISIN to its available asset listing codes.
        """
        sql = """
            SELECT asset_isin, asset_listing_code
            FROM asset_listings
            WHERE asset_isin = ANY(%s)
        """
        result = {}
        with self.get_connection() as conn, conn.cursor() as cur:
            cur.execute(sql, (ISINs,))
            for isin, code in cur.fetchall():
                result.setdefault(isin, []).append(code)
        return result

    def get_asset_listing_codes_by_asset_class(self, asset_class_name: str) -> List[str]:
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                query = """
                        SELECT al.asset_listing_code
                        FROM asset_listings al
                                 JOIN asset_metadata am ON al.asset_isin = am.isin
                                 JOIN asset_classes ac ON am.asset_class_id = ac.id
                        WHERE ac.name = %s 
                        AND am.distribution_policy = 'Accumulating'
                        """
                cur.execute(query, (asset_class_name,))
                rows = cur.fetchall()

        return [row[0] for row in rows]
