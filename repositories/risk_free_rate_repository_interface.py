from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional, Tuple
import pandas as pd


class RiskFreeRateRepositoryInterface(ABC):
    """
    Interface for risk-free rate repositories.
    Implementations should handle risk-free rate data retrieval operations.
    """

    def get_risk_free_rates_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: Optional[datetime] = None
    ) -> Tuple[pd.DataFrame, datetime]:
        """
        Retrieve risk-free rates within a specified time horizon up to a given date.

        Args:
            time_horizon_in_years (int): Number of years for the time horizon.
            last_date (Optional[datetime]): The last date to consider for rates.

        Returns:
            Tuple[pd.DataFrame, datetime]: DataFrame of rates and the last document date.
        """
        pass

    @abstractmethod
    def get_avg_risk_free_rate_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: Optional[datetime] = None
    ) -> float:
        """
        Retrieve the average risk-free rate over a specified time horizon up to a given date.

        Args:
            time_horizon_in_years (int): Number of years for the time horizon.
            last_date (Optional[datetime]): The last date to consider for rates.

        Returns:
            float: The average risk-free rate.
        """
        pass

    @abstractmethod
    def get_last_risk_free_rate(
            self,
            last_date: Optional[datetime] = None
    ) -> float:
        """
        Retrieve the most recent risk-free rate up to a given date.

        Args:
            last_date (Optional[datetime]): The last date to consider for rates.

        Returns:
            float: The most recent risk-free rate.
        """
        pass