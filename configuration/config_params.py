CONNECTION_PARAMS = {
        'dbname': 'financial_data_9zmf',
        'user': 'valusense',
        'password': 'lznTSU7Z6b2aUo1CBvTzjulrUziYn1Uv',
        'host': 'dpg-d14q7tq4d50c73ck8bt0-a.frankfurt-postgres.render.com',
        'port': '5432'
    }

ASSET_CLASS_MARKET_CAPS_TABLE = 'asset_class_market_caps'
ASSET_CLASSES_TABLE = 'asset_classes'
ASSET_LISTING_COVARIANCES_TABLE = 'asset_listing_covariances'
ASSET_LISTING_DATA_TABLE = 'asset_listing_data'
ASSET_LISTING_EXPECTED_RETURNS_TABLE = 'asset_listing_expected_returns'
ASSET_LISTING_RETURN_SIMULATIONS_TABLE = 'asset_listing_return_simulations'
ASSET_LISTINGS_TABLE = 'asset_listings'
ASSET_METADATA_TABLE = 'asset_metadata'
CURRENCIES_TABLE = 'currencies'
EXCHANGES_TABLE = 'exchanges'
FX_RATES_TABLE = 'fx_rates'
RISK_FREE_RATES_TABLE = 'risk_free_rates'

