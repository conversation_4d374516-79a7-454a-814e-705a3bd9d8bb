from dateutil.relativedelta import relativedelta
from pydantic import BaseModel, Field, field_validator, ConfigDict, computed_field
from typing import List, Optional
from datetime import datetime, date
import numpy as np

from factories.return_calculations import ReturnCalculator
from factories.return_measures import ReturnMeasure
from factories.risk_measures import RiskMeasure
from schemas.constants import ReinvestingFrequencyEnum, RiskMeasureEnum, ReturnMeasureEnum, ReturnCalculationEnum


class ProductMetadata(BaseModel):
    isin: str = Field(min_length=12, max_length=12, description='ISIN of the product')
    price: Optional[float] = Field(gt=0, description='Price of the product', default=100)
    fundName: str = Field(description='Name of the Exchange Traded Fund')
    assetClass: str = Field(description='Asset class of the product')
    assetSubclass: str = Field(description='Asset subclass of the product')
    benchmarkIndex: str = Field(description='Benchmark index which is replicated by this fund')
    ter: float = Field(ge=0, lt=1, description='Annual cost of fund')
    stockExchange: str = Field(description='The stock exchange where the product is traded')
    ticker: str = Field(description='Product ticker for a specific stock exchange')
    tradeCurrency: str = Field(description='Currency in which the product is traded')
    ibkrLink: str = Field(description='Interactive Brokers link for the product')


class AssetMetadata(BaseModel):
    isin: str = Field(min_length=12, max_length=12, description="ISIN of the product")
    fund_name: str = Field(description="Name of the fund")
    ter: float = Field(ge=0, le=9.99, description="Total expense ratio (max 9.99%)")
    inception_date: Optional[date] = Field(description="Inception date of the fund")
    fund_size_in_m: Optional[float] = Field(ge=0, description="Fund size in millions")
    distribution_policy: str = Field(description="Distribution policy (e.g., accumulating/distributing)")
    replication: Optional[str] = Field(description="Replication method (e.g., physical, synthetic)")
    is_sustainable: Optional[bool] = Field(description="Whether the fund is ESG/sustainable")
    asset_class_name: str = Field(description="Asset class")
    index: Optional[str] = Field(description="Benchmark index tracked by the fund")
    investment_focus: Optional[str] = Field(description="Investment focus of the fund")
    legal_structure: Optional[str] = Field(description="Legal structure of the fund (e.g., UCITS)")
    strategy_risk: Optional[str] = Field(description="Risk strategy classification")
    fund_currency: str = Field(min_length=3, max_length=3, description="Currency of the fund")
    currency_risk: Optional[str] = Field(description="Currency risk exposure")
    distribution_frequency: Optional[str] = Field(description="Frequency of distributions (e.g., quarterly)")
    fund_domicile: Optional[str] = Field(description="Country of domicile")
    fund_provider: Optional[str] = Field(description="Fund provider or issuer")


class Product(BaseModel):
    isin: str = Field(min_length=12, max_length=12, description='ISIN of the product')
    weight: float = Field(gt=0, le=1, description='Weight of the product in portfolio')
    quantity: Optional[int] = Field(gt=0, description='Number of shares of this product in portfolio', default=None)


class FrontierPortfolio(BaseModel):
    model_config = ConfigDict(populate_by_name=True)
    index: int = Field(ge=0, description='Index of the portfolio')
    annual_return: float = Field(description='Expected annual return of the portfolio', alias='annualReturn')
    variance: float = Field(ge=0, description='Expected annual variance of the portfolio')
    sharpe_ratio: Optional[float] = Field(description='The Sharpe ratio of the portfolio', alias='sharpeRatio', default=None)
    products: List[Product]


class FrontierStrategy(BaseModel):
    model_config = ConfigDict(populate_by_name=True)
    index: int = Field(ge=0, description='Index of the strategy')
    annual_return: float = Field(description='Annual return of the strategy', alias='annualReturn')
    annual_risk: float = Field(description='Annual risk of the strategy', alias='annualRisk')
    sharpe_ratio: Optional[float] = Field(description='Sharpe ratio of the strategy', alias='sharpeRatio', default=None)
    strategy: List[int] = Field(description='List of portfolio indices contained in strategy.')


class PortfolioOnDate(BaseModel):
    point_datetime: datetime = Field(description='Date and time of the portfolio point')
    portfolio_value: float = Field(description='Value of the portfolio')
    weights: List[float] = Field(gt=0, le=1, description='Weights of the portfolio')


class PurchaseLot(BaseModel):
    quantity_purchased: int = Field(description='Number of shares of this product purchased', alias='quantityPurchased')
    purchase_date: datetime = Field(description='Datetime of the purchase', default_factory=datetime.now, alias='purchaseDate')
    unit_purchase_price: float = Field(gt=0, description='Unit price of the purchase', alias='unitPurchasePrice')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)

    @field_validator('quantity_purchased')
    @classmethod
    def quantity_validator(cls, value: int) -> int:
        if value == 0:
            raise ValueError('Quantity cannot be zero')
        return value


class RebalanceConstrains(BaseModel):
    allowed_capital_gains: Optional[float] = Field(default=None, description='Allowed capital gains of the rebalance', alias='allowedCapitalGains')
    allowed_trade_cost: Optional[float] = Field(default=None, description='Allowed transaction fees of the rebalance', alias='allowedTradeCost')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)


class UserPreferences(BaseModel):
    reinvesting_frequency: ReinvestingFrequencyEnum = Field(default=ReinvestingFrequencyEnum.YEARLY, description='The reinvesting frequency.', alias='reinvestingFrequency')
    return_measure_type: Optional[ReturnMeasureEnum] = Field(default=ReturnMeasureEnum.EXPECTED, description='Return measure type.', alias='returnMeasureType')
    risk_measure_type: Optional[RiskMeasureEnum] = Field(default=RiskMeasureEnum.CVAR, description='Risk measure type.', alias='riskMeasureType')
    return_calculation_type: Optional[ReturnCalculationEnum] = Field(default=ReturnCalculationEnum.MWR, description='Return calculation type.', alias='returnCalculationType')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)


class PreferenceBasedCalculations:
    def __init__(self, risk_measure: RiskMeasure, return_measure: ReturnMeasure, return_calculator: ReturnCalculator):
        self.risk_measure = risk_measure
        self.return_measure = return_measure
        self.return_calculator = return_calculator


class AssetPurchaseLots(BaseModel):
    isin: str = Field(min_length=12, max_length=12, description='ISIN of the product')
    purchase_lots: List[PurchaseLot] = Field(description='List of purchase lots of the product', alias='purchaseLots')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)


    #TODO move those methods out of BaseModel classes, keep data validation and business logic separated
    def sum_quantities(self):
        return np.sum([purchase_lot.quantity_purchased for purchase_lot in self.purchase_lots])

    def get_prices(self):
        return np.array([purchase_lot.unit_purchase_price for purchase_lot in self.purchase_lots]).reshape(-1, 1)

    def get_quantities(self):
        return np.array([purchase_lot.quantity_purchased for purchase_lot in self.purchase_lots]).reshape(-1, 1)

    def get_ages(self, date: Optional[datetime] = None):
        if date is None:
            date = datetime.now()
        return np.array([
            relativedelta(date, purchase_lot.purchase_date).years
            for purchase_lot in self.purchase_lots
        ]).reshape(-1, 1)

    def __str__(self):
        return "\n".join(
            f"Quantity: {purchase_lot.quantity_purchased}, Price: {purchase_lot.unit_purchase_price}, Purchase calculate_on_date: {purchase_lot.purchase_date}" for purchase_lot in self.purchase_lots
        )


class PortfolioPurchaseLots(BaseModel):
    asset_purchase_lots: List[AssetPurchaseLots] = Field(description="List of asset purchase lots in the portfolio.", alias='assetPurchaseLots')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)


    def get_quantities(self):
        return np.array([asset_purchase_lot.sum_quantities() for asset_purchase_lot in self.asset_purchase_lots]).reshape(-1, 1)

    def is_empty(self) -> bool:
        return all(len(asset.purchase_lots) == 0 for asset in self.asset_purchase_lots)

    def __str__(self):
        return "\n\n".join(
            f"Asset {i + 1}:\n{asset_purchase_lot}" for i, asset_purchase_lot in enumerate(self.asset_purchase_lots)
        )


class RebalancedAsset(BaseModel):
    isin: str = Field(min_length=12, max_length=12, description='ISIN of the asset')
    new_quantity: int = Field(ge=0, description='New quantity of the asset', alias='newQuantity')
    delta_quantity: int = Field(description='Delta quantity of the asset', alias='deltaQuantity')
    new_weight: float = Field(ge=0, le=1, description='New weight of the asset', alias='newWeight')
    capital_gain: float = Field(description='Capital gain realized by the rebalance', alias='capitalGain')
    transaction_cost: float = Field(ge=0, description='PurchaseLot cost of the rebalance', alias='transactionCost')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)


class RebalancedPortfolio(BaseModel):
    rebalanced_assets: List[RebalancedAsset] = Field(description='List of rebalanced assets', alias='rebalancedAssets')
    new_mark_to_market_portfolio_value: float = Field(description='Mark to market value of the portfolio after rebalance', alias='newMarkToMarketPortfolioValue')
    new_cash: float = Field(description='Cash left after rebalance', alias='newCash')

    @computed_field(alias='totalCapitalGains')
    @property
    def total_capital_gains(self) -> float:
        return sum(asset.capital_gain for asset in self.rebalanced_assets)

    @computed_field(alias='totalTransactionCost')
    @property
    def total_transaction_cost(self) -> float:
        return sum(asset.transaction_cost for asset in self.rebalanced_assets)

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)


class RebalanceResults(BaseModel):
    new_quantities: np.ndarray = Field(description='List of new quantities', alias='newQuantities', default=None)
    delta_quantities: np.ndarray = Field(description='List of delta quantities', alias='deltaQuantities', default=None)
    new_weights: np.ndarray = Field(description='List of new weights', alias='newWeights', default=None)
    per_asset_cg: dict[str, float] = Field(description='Dictionary with capital gains for each asset', alias='perAssetCg', default_factory=dict)
    trade_costs: np.ndarray = Field(description='List of traded costs', alias='tradeCosts', default=None)
    new_portfolio_value: float = Field(description='New portfolio value after rebalance', alias='newPortfolioValue', default=None)
    new_cash: float = Field(description='Cash left after rebalance', alias='newCash', default=None)
    tracking_error: float = Field(description='Tracking error', alias='trackingError', default=None)

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True, arbitrary_types_allowed=True)
